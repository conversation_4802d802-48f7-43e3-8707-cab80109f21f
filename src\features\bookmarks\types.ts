import { APIResponse } from "@/lib/api/types";

export type getBookmarksReq<C> = {
	category: C;
	page: number;
	limit: number;
};

type Pagination = {
	currentPage: number;
	limit: number;
	totalItems: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPrevPage: boolean;
};

type Count = {
	resources: number;
	mcqs: number;
	quizzes: number;
	currentPageTotal: number;
};

type BaseApiResponse<T> = {
	status: number;
	success: boolean;
	message: string;
	data: T & {
		pagination: Pagination;
		count: Count;
	};
};

type ResourceData = {
	resources: any[];
	totalResources: number;
};

type McqData = {
	mcqs: any[];
	totalMcqs: number;
};

type QuizData = {
	quizzes: any[];
	totalQuizzes: number;
};

export type getBookmarksResp = {
	quiz: BaseApiResponse<QuizData>;
	mcq: BaseApiResponse<McqData>;
	resource: BaseApiResponse<ResourceData>;
};

export type addBookmarkReq = {
	category: "resource" | "mcq" | "quiz";
	id: string;
};

// Quiz types for single quiz API
export interface QuizMCQ {
	_id: string;
	subject: string;
	type: string;
	title: string;
	topic: string;
	difficulty: "easy" | "medium" | "hard";
	repitition: boolean;
	resource?: string;
	explanation?: string;
	entryTest: string[];
	options: string[];
	answer?: number; // Optional because it might be excluded for incomplete quizzes
	chapter: number;
	chosenOption?: number | null; // User's chosen option
}

export interface QuizResponse {
	_id: string;
	type: string;
	entryTest: string;
	createdAt: string;
	completed: boolean;
	totalTime: number;
	timeTaken: number;
	subtype: string;
	bookmarked: boolean;
	mcqs: QuizMCQ[];
	user_id: string;
}

export type getQuizResponse = APIResponse<QuizResponse>;
