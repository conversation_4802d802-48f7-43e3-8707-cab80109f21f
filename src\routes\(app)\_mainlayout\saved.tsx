import { createFile<PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
	Bookmark,
	Clock,
	Calendar,
	ChevronRight,
	AlertTriangle,
} from "react-feather";
import { useGetBookmarks } from "@/lib/queries/tests.query";
import { useToast } from "@/hooks/use-toast";
import { Navbar } from "@/components/layout/main/navbar";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import MobileHeader from "@/components/common/mobile-header";
import { QuizMCQ } from "@/features/bookmarks/types";
import { deleteBookmark } from "@/features/bookmarks/services";
import { toggleMCQType } from "@/lib/utils";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";

interface SavedTest {
	_id: string;
	type: string;
	entryTest: string;
	createdAt: string;
	completed: boolean;
	totalTime: number;
	timeTaken: number;
	subtype: string;
	bookmarked: boolean;
	mcqs: any[];
}

const SavedTestCard = ({
	test,
	onUnbookmark,
}: {
	test: SavedTest;
	onUnbookmark: (id: string) => void;
}) => {
	const navigate = useNavigate();
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(true); // Initially bookmarked since it's in saved list

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const getTestTitle = () => {
		return `${test.entryTest} ${test.type === "mock" ? "Mock" : "Custom"} Test`;
	};

	const getLastScore = () => {
		if (!test.completed) return "Not completed";
		// This would need to be calculated from the actual MCQ results
		// For now, showing placeholder
		return "230/300";
	};

	const handleAttempt = () => {
		navigate({ to: `/quiz/${test._id}` });
	};

	const handleToggleBookmark = async () => {
		try {
			const response = await deleteBookmark({ category: "quiz", id: test._id });
			if (response?.data?.status === 200 && response?.data?.success) {
				toast({
					title: "Success",
					description: "Test bookmark removed!",
				});
				setIsBookmarked(false); // Update local state for visual feedback
				onUnbookmark(test._id);
			} else {
				toast({
					title: "Error",
					description: "Failed to remove the test bookmark",
					variant: "destructive",
				});
			}
		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to remove the test bookmark",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				<div className="flex items-start justify-between mb-4">
					<div className="flex-1">
						<h3 className="text-lg font-semibold text-gray-900 mb-2">
							{getTestTitle()}
						</h3>
						<div className="flex items-center gap-4 text-sm text-gray-600">
							<div className="flex items-center gap-1">
								<Clock className="w-4 h-4" />
								<span>last scored: {getLastScore()}</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="w-4 h-4" />
								<span>last attempted: {formatDate(test.createdAt)}</span>
							</div>
						</div>
					</div>
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={handleToggleBookmark}
						title={isBookmarked ? "Remove bookmark" : "Bookmark this test"}
					>
						<Bookmark className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`} />
					</Button>
				</div>

				<Button
					onClick={handleAttempt}
					className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center gap-2"
				>
					Attempt
					<ChevronRight className="w-4 h-4" />
				</Button>
			</CardContent>
		</Card>
	);
};

const SavedMCQCard = ({
	mcq,
	numbering,
	onUnbookmark,
}: {
	mcq: QuizMCQ;
	numbering: number;
	onUnbookmark: (id: string) => void;
}) => {
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(true); // Initially bookmarked since it's in saved list

	const difficultyStyles = {
		easy: "border-green-300 bg-green-100 text-green-800",
		medium: "border-yellow-300 bg-yellow-100 text-yellow-800",
		hard: "border-red-300 bg-red-100 text-red-800",
	};

	const handleToggleBookmark = async () => {
		try {
			const response = await deleteBookmark({ category: "mcq", id: mcq._id });
			if (response?.data?.status === 200 && response?.data?.success) {
				toast({
					title: "Success",
					description: "MCQ bookmark removed!",
				});
				setIsBookmarked(false); // Update local state for visual feedback
				onUnbookmark(mcq._id);
			} else {
				toast({
					title: "Error",
					description: "Failed to remove the MCQ bookmark",
					variant: "destructive",
				});
			}
		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to remove the MCQ bookmark",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-2">
						<p className="text-gray-500 font-bold">QUESTION #{numbering + 1}</p>
						{mcq.type && (
							<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
								{toggleMCQType(mcq.type)}
							</span>
						)}
						{mcq.difficulty && (
							<span
								className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${difficultyStyles[mcq.difficulty] || ""}`}
							>
								{mcq.difficulty}
							</span>
						)}
					</div>
					<div className="flex items-center gap-4 mx-4">
						<Button
							variant="icon"
							className="[&_svg]:size-5 px-0"
							onClick={handleToggleBookmark}
							title={isBookmarked ? "Remove bookmark" : "Bookmark this MCQ"}
						>
							<Bookmark className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`} />
						</Button>
					</div>
				</div>

				<h3 className="mb-6 text-lg font-medium">
					<Latex>{mcq.title}</Latex>
				</h3>

				<div className="space-y-2">
					<p className="font-bold text-gray-400 mb-4">OPTIONS</p>
					{mcq.options.map((option, index) => (
						<div
							key={mcq._id + option + index}
							className={`p-2 rounded ${index === mcq.answer ? "bg-green-50 border border-green-200" : "bg-gray-50"}`}
						>
							<span
								className={`text-gray-400 ${index === mcq.answer ? "font-bold text-green-600" : ""}`}
							>
								({String.fromCharCode(65 + index)})
							</span>
							<span
								className={`text-base font-medium ml-2 ${index === mcq.answer ? "text-green-600" : ""}`}
							>
								<Latex>{option}</Latex>
							</span>
							{index === mcq.answer && (
								<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
									Correct Answer
								</span>
							)}
						</div>
					))}
				</div>

				{mcq.explanation && (
					<div className="mt-4 p-4 bg-gray-50 rounded-md">
						<p className="text-sm font-medium text-gray-700 mb-2">
							Explanation:
						</p>
						<p className="text-sm text-gray-600">
							<Latex>{mcq.explanation}</Latex>
						</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
};

const SavedPage = () => {
	const [activeTab, setActiveTab] = useState("tests");
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Fetch saved tests
	const {
		data: savedTestsData,
		isLoading: testsLoading,
		error: testsError,
	} = useGetBookmarks({
		category: "quiz",
		page: 1,
		limit: 20,
	});

	// Fetch saved MCQs
	const {
		data: savedMCQsData,
		isLoading: mcqsLoading,
		error: mcqsError,
	} = useGetBookmarks({
		category: "mcq",
		page: 1,
		limit: 20,
	});

	const savedTests = savedTestsData?.quizzes || [];
	const savedMCQs = savedMCQsData?.mcqs || [];

	const handleMCQUnbookmark = (_mcqId: string) => {
		// Note: The item will remain visible until page refresh as requested
		// This is intentional behavior as per user requirements
	};

	const handleTestUnbookmark = (_testId: string) => {
		// Note: The item will remain visible until page refresh as requested
		// This is intentional behavior as per user requirements
	};

	if (testsError || mcqsError) {
		toast({
			title: "Error",
			description: "Failed to load saved items. Please try again.",
			variant: "destructive",
		});
	}

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}

			<div className="container mx-auto p-4 pb-20">
				{!isDesktop && <MobileHeader title="Saved" />}

				<div className="max-w-6xl mx-auto">
					<h1 className="text-2xl font-bold text-gray-900 mb-6">Saved</h1>

					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="grid w-full grid-cols-2 mb-6">
							<TabsTrigger value="mcqs">Saved MCQs</TabsTrigger>
							<TabsTrigger value="tests">Saved Tests</TabsTrigger>
						</TabsList>

						<TabsContent value="mcqs" className="space-y-4">
							{mcqsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-32 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedMCQs.length > 0 ? (
								<div className="flex flex-col gap-4">
									{savedMCQs.map((mcq: QuizMCQ, index: number) => (
										<SavedMCQCard
											key={mcq._id}
											mcq={mcq}
											numbering={index}
											onUnbookmark={handleMCQUnbookmark}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved MCQs
									</h3>
									<p className="text-gray-600">
										Start bookmarking MCQs to see them here.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="tests" className="space-y-4">
							{testsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-40 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedTests.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{savedTests.map((test: SavedTest) => (
										<SavedTestCard
											key={test._id}
											test={test}
											onUnbookmark={handleTestUnbookmark}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved tests
									</h3>
									<p className="text-gray-600">
										Start bookmarking tests to see them here.
									</p>
								</div>
							)}
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/saved")({
	component: SavedPage,
});
