import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
	Bookmark,
	Clock,
	Calendar,
	ChevronRight,
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	Send,
} from "react-feather";
import { useGetBookmarks } from "@/lib/queries/tests.query";
import { useToast } from "@/hooks/use-toast";
import { Navbar } from "@/components/layout/main/navbar";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import MobileHeader from "@/components/common/mobile-header";
import { QuizMCQ } from "@/features/bookmarks/types";
import { deleteBookmark, addBookmark } from "@/features/bookmarks/services";
import { toggleMCQType } from "@/lib/utils";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import ReportMCQDialog from "@/components/layout/mcqs/report-mcq-dialog";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";

interface SavedTest {
	_id: string;
	type: string;
	entryTest: string;
	createdAt: string;
	completed: boolean;
	totalTime: number;
	timeTaken: number;
	subtype: string;
	bookmarked: boolean;
	mcqs: any[];
}

const SavedTestCard = ({
	test,
	onUnbookmark,
}: {
	test: SavedTest;
	onUnbookmark: (id: string) => void;
}) => {
	const navigate = useNavigate();
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(true); // Initially bookmarked since it's in saved list

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const getTestTitle = () => {
		return `${test.entryTest} ${test.type === "mock" ? "Mock" : "Custom"} Test`;
	};

	const getLastScore = () => {
		if (!test.completed) return "Not completed";
		// This would need to be calculated from the actual MCQ results
		// For now, showing placeholder
		return "230/300";
	};

	const handleAttempt = () => {
		navigate({ to: `/quiz/${test._id}` });
	};

	const handleToggleBookmark = async () => {
		try {
			if (isBookmarked) {
				// Remove bookmark
				const response = await deleteBookmark({
					category: "quiz",
					id: test._id,
				});
				if (response?.data?.status === 200 && response?.data?.success) {
					toast({
						title: "Success",
						description: "Test bookmark removed!",
					});
					setIsBookmarked(false);
					onUnbookmark(test._id);
				} else {
					toast({
						title: "Error",
						description: "Failed to remove the test bookmark",
						variant: "destructive",
					});
				}
			} else {
				// Add bookmark
				const response = await addBookmark({ category: "quiz", id: test._id });
				if (response?.data?.status === 200 && response?.data?.success) {
					toast({
						title: "Success",
						description: "Test bookmarked!",
					});
					setIsBookmarked(true);
				} else {
					toast({
						title: "Error",
						description: "Failed to bookmark the test",
						variant: "destructive",
					});
				}
			}
		} catch (error) {
			toast({
				title: "Error",
				description: isBookmarked
					? "Failed to remove the test bookmark"
					: "Failed to bookmark the test",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				<div className="flex items-start justify-between mb-4">
					<div className="flex-1">
						<h3 className="text-lg font-semibold text-gray-900 mb-2">
							{getTestTitle()}
						</h3>
						<div className="flex items-center gap-4 text-sm text-gray-600">
							<div className="flex items-center gap-1">
								<Clock className="w-4 h-4" />
								<span>last scored: {getLastScore()}</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="w-4 h-4" />
								<span>last attempted: {formatDate(test.createdAt)}</span>
							</div>
						</div>
					</div>
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={handleToggleBookmark}
						title={isBookmarked ? "Remove bookmark" : "Bookmark this test"}
					>
						<Bookmark
							className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`}
						/>
					</Button>
				</div>

				<Button
					onClick={handleAttempt}
					className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center gap-2"
				>
					Attempt
					<ChevronRight className="w-4 h-4" />
				</Button>
			</CardContent>
		</Card>
	);
};

const SavedMCQCard = ({
	mcq,
	numbering,
	onUnbookmark,
}: {
	mcq: QuizMCQ;
	numbering: number;
	onUnbookmark: (id: string) => void;
}) => {
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(true); // Initially bookmarked since it's in saved list
	const [showExpertAnswer, setShowExpertAnswer] = useState(false);
	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
	const [showChatbot, setShowChatbot] = useState(false);
	const [chatbotQuestion, setChatbotQuestion] = useState("");
	const [chatbotResponse, setChatbotResponse] = useState("");

	// Grok key status and chatbot mutation
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	const difficultyStyles = {
		easy: "border-green-300 bg-green-100 text-green-800",
		medium: "border-yellow-300 bg-yellow-100 text-yellow-800",
		hard: "border-red-300 bg-red-100 text-red-800",
	};

	const handleToggleBookmark = async () => {
		try {
			if (isBookmarked) {
				// Remove bookmark
				const response = await deleteBookmark({ category: "mcq", id: mcq._id });
				if (response?.data?.status === 200 && response?.data?.success) {
					toast({
						title: "Success",
						description: "MCQ bookmark removed!",
					});
					setIsBookmarked(false);
					onUnbookmark(mcq._id);
				} else {
					toast({
						title: "Error",
						description: "Failed to remove the MCQ bookmark",
						variant: "destructive",
					});
				}
			} else {
				// Add bookmark
				const response = await addBookmark({ category: "mcq", id: mcq._id });
				if (response?.data?.status === 200 && response?.data?.success) {
					toast({
						title: "Success",
						description: "MCQ bookmarked!",
					});
					setIsBookmarked(true);
				} else {
					toast({
						title: "Error",
						description: "Failed to bookmark the MCQ",
						variant: "destructive",
					});
				}
			}
		} catch (error) {
			toast({
				title: "Error",
				description: isBookmarked
					? "Failed to remove the MCQ bookmark"
					: "Failed to bookmark the MCQ",
				variant: "destructive",
			});
		}
	};

	const handleChatbotSubmit = async () => {
		if (!chatbotQuestion.trim()) {
			toast({
				title: "Error",
				description: "Please enter a question",
				variant: "destructive",
			});
			return;
		}

		const correctAnswer = `${String.fromCharCode(65 + (mcq.answer || 0))}.${mcq.options[mcq.answer || 0]}`;

		const payload = {
			mcqid: mcq._id,
			mcqTitle: mcq.title,
			options: mcq.options.map(
				(option, index) => `${String.fromCharCode(65 + index)}.${option}`
			),
			userChoice: "No answer selected", // For saved MCQs, no user choice
			correctAnswer,
			explanation: mcq.explanation || "",
			question: chatbotQuestion,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			const content = response.data.result?.choices?.[0]?.message?.content;
			if (content) {
				setChatbotResponse(content);
				setChatbotQuestion(""); // Clear the input after successful submission
			} else {
				throw new Error("Invalid response format");
			}
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-2">
						<p className="text-gray-500 font-bold">QUESTION #{numbering + 1}</p>
						{mcq.type && (
							<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
								{toggleMCQType(mcq.type)}
							</span>
						)}
						{mcq.difficulty && (
							<span
								className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${difficultyStyles[mcq.difficulty] || ""}`}
							>
								{mcq.difficulty}
							</span>
						)}
					</div>
					<div className="flex items-center gap-4 mx-4">
						<Button
							variant="icon"
							className="[&_svg]:size-5 px-0"
							onClick={handleToggleBookmark}
							title={isBookmarked ? "Remove bookmark" : "Bookmark this MCQ"}
						>
							<Bookmark
								className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`}
							/>
						</Button>
						<Button
							variant="icon"
							className="[&_svg]:size-5 px-0"
							onClick={() => setIsReportDialogOpen(true)}
							title="Report this MCQ"
						>
							<AlertTriangle className="w-5 h-5 text-gray-600 hover:text-red-500" />
						</Button>
					</div>
				</div>

				<h3 className="mb-6 text-lg font-medium">
					<Latex>{mcq.title}</Latex>
				</h3>

				<div className="space-y-2">
					<p className="font-bold text-gray-400 mb-4">OPTIONS</p>
					{mcq.options.map((option, index) => (
						<div
							key={mcq._id + option + index}
							className={`p-2 rounded ${index === mcq.answer ? "bg-green-50 border border-green-200" : "bg-gray-50"}`}
						>
							<span
								className={`text-gray-400 ${index === mcq.answer ? "font-bold text-green-600" : ""}`}
							>
								({String.fromCharCode(65 + index)})
							</span>
							<span
								className={`text-base font-medium ml-2 ${index === mcq.answer ? "text-green-600" : ""}`}
							>
								<Latex>{option}</Latex>
							</span>
							{index === mcq.answer && (
								<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
									Correct Answer
								</span>
							)}
						</div>
					))}
				</div>

				{/* Expert Answer Section */}
				<div className="mt-4">
					<div className="flex flex-col item-start sm:flex-row sm:items-center sm:justify-end gap-4">
						{/* <div className="py-2 px-4 rounded-full max-w-[90%] bg-green-100 text-green-700 flex items-center gap-2">
							<span className="text-sm flex items-center gap-2">
								<CheckCircle className="w-4 h-4" />
								This is a saved MCQ with the correct answer shown.
							</span>
						</div> */}
						<Button
							variant="link"
							onClick={() => setShowExpertAnswer(!showExpertAnswer)}
							className="justify-start text-gray-600 flex items-center gap-1 text-sm"
						>
							{showExpertAnswer ? "Hide" : "View"} expert answer
							{showExpertAnswer ? (
								<ChevronUp size={16} />
							) : (
								<ChevronDown size={16} />
							)}
						</Button>
					</div>

					{showExpertAnswer && (
						<div className="mt-2 p-4 bg-gray-50 rounded-md">
							{mcq.explanation && (
								<p className="text-sm">
									<Latex>{mcq.explanation}</Latex>
								</p>
							)}
							{/* <div className="mt-2 pt-2 border-t border-gray-200">
								<p className="text-sm font-medium text-green-600">
									Correct answer:{" "}
									{String.fromCharCode(65 + (mcq.answer || 0))} -{" "}
									<Latex>
										{String(mcq.options[mcq.answer || 0] ?? "")}
									</Latex>
								</p>
							</div> */}
						</div>
					)}

					{/* Ask Chatbot Section */}
					{showExpertAnswer && (
						<div className="mt-4">
							{/* Ask Chatbot Button */}
							<div className="mb-4">
								<Button
									variant="outline"
									onClick={() => setShowChatbot(!showChatbot)}
									className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
								>
									<span className="text-lg">🤖</span>
									Ask Chatbot
								</Button>
							</div>

							{/* Chatbot Input Section */}
							{showChatbot && (
								<div className="space-y-4">
									{/* Grok Key Banner */}
									{!chatbotResponse && (
										<GrokKeyBanner
											hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
											className="mb-4"
										/>
									)}

									{/* Chatbot */}
									<div className="rounded-md">
										{chatbotResponse && (
											<div className="flex items-start gap-3 mb-4 border-b border-gray-200 pb-4">
												<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
													<span className="text-sm">🤖</span>
												</div>
												<div className="flex-1">
													<p className="text-sm font-medium text-gray-800 mb-1">
														Chatbot says...
													</p>
													<p className="text-sm text-gray-700 whitespace-pre-wrap">
														{chatbotResponse}
													</p>
												</div>
											</div>
										)}

										{/* Ask Chatbot input */}
										<div className="flex gap-2">
											<Input
												placeholder="Ask Chatbot..."
												value={chatbotQuestion}
												onChange={(e) => setChatbotQuestion(e.target.value)}
												disabled={
													chatbotMutation.isPending ||
													!grokKeyStatus?.has_groq_api_key
												}
												className="flex-1"
												onKeyDown={(e) => {
													if (
														e.key === "Enter" &&
														grokKeyStatus?.has_groq_api_key
													) {
														handleChatbotSubmit();
													}
												}}
											/>
											<Button
												onClick={handleChatbotSubmit}
												disabled={
													chatbotMutation.isPending ||
													!chatbotQuestion.trim() ||
													!grokKeyStatus?.has_groq_api_key
												}
												className="bg-purple-600 hover:bg-purple-700"
											>
												{chatbotMutation.isPending ? (
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
												) : (
													<Send size={16} />
												)}
											</Button>
										</div>
									</div>
								</div>
							)}
						</div>
					)}
				</div>

				<ReportMCQDialog
					open={isReportDialogOpen}
					onOpenChange={setIsReportDialogOpen}
					mcqId={mcq._id}
				/>
			</CardContent>
		</Card>
	);
};

const SavedPage = () => {
	const [activeTab, setActiveTab] = useState("tests");
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Fetch saved tests
	const {
		data: savedTestsData,
		isLoading: testsLoading,
		error: testsError,
	} = useGetBookmarks({
		category: "quiz",
		page: 1,
		limit: 20,
	});

	// Fetch saved MCQs
	const {
		data: savedMCQsData,
		isLoading: mcqsLoading,
		error: mcqsError,
	} = useGetBookmarks({
		category: "mcq",
		page: 1,
		limit: 20,
	});

	const savedTests = savedTestsData?.quizzes || [];
	const savedMCQs = savedMCQsData?.mcqs || [];

	const handleMCQUnbookmark = (_mcqId: string) => {
		// Note: The item will remain visible until page refresh as requested
		// This is intentional behavior as per user requirements
	};

	const handleTestUnbookmark = (_testId: string) => {
		// Note: The item will remain visible until page refresh as requested
		// This is intentional behavior as per user requirements
	};

	if (testsError || mcqsError) {
		toast({
			title: "Error",
			description: "Failed to load saved items. Please try again.",
			variant: "destructive",
		});
	}

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}

			<div className="container mx-auto p-4 pb-20">
				{!isDesktop && <MobileHeader title="Saved" />}

				<div className="max-w-6xl mx-auto">
					<h1 className="text-2xl font-bold text-gray-900 mb-6">Saved</h1>

					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="grid w-full grid-cols-2 mb-6">
							<TabsTrigger value="mcqs">Saved MCQs</TabsTrigger>
							<TabsTrigger value="tests">Saved Tests</TabsTrigger>
						</TabsList>

						<TabsContent value="mcqs" className="space-y-4">
							{mcqsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-32 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedMCQs.length > 0 ? (
								<div className="flex flex-col gap-4">
									{savedMCQs.map((mcq: QuizMCQ, index: number) => (
										<SavedMCQCard
											key={mcq._id}
											mcq={mcq}
											numbering={index}
											onUnbookmark={handleMCQUnbookmark}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved MCQs
									</h3>
									<p className="text-gray-600">
										Start bookmarking MCQs to see them here.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="tests" className="space-y-4">
							{testsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-40 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedTests.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{savedTests.map((test: SavedTest) => (
										<SavedTestCard
											key={test._id}
											test={test}
											onUnbookmark={handleTestUnbookmark}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved tests
									</h3>
									<p className="text-gray-600">
										Start bookmarking tests to see them here.
									</p>
								</div>
							)}
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/saved")({
	component: SavedPage,
});
