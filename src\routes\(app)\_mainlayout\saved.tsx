import { createFile<PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Bookmark, Clock, Calendar, ChevronRight } from "react-feather";
import { useGetBookmarks } from "@/lib/queries/tests.query";
import { useToast } from "@/hooks/use-toast";
import { Navbar } from "@/components/layout/main/navbar";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import MobileHeader from "@/components/common/mobile-header";
import { QuizMCQ } from "@/features/bookmarks/types";

interface SavedTest {
	_id: string;
	type: string;
	entryTest: string;
	createdAt: string;
	completed: boolean;
	totalTime: number;
	timeTaken: number;
	subtype: string;
	bookmarked: boolean;
	mcqs: any[];
}

const SavedTestCard = ({ test }: { test: SavedTest }) => {
	const navigate = useNavigate();

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	// const formatTime = (seconds: number) => {
	// 	const hours = Math.floor(seconds / 3600);
	// 	const minutes = Math.floor((seconds % 3600) / 60);
	// 	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	// };

	const getTestTitle = () => {
		return `${test.entryTest} ${test.type === "mock" ? "Mock" : "Custom"} Test`;
	};

	const getLastScore = () => {
		if (!test.completed) return "Not completed";
		// This would need to be calculated from the actual MCQ results
		// For now, showing placeholder
		return "230/300";
	};

	const handleAttempt = () => {
		navigate({ to: `/quiz/${test._id}` });
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				<div className="flex items-start justify-between mb-4">
					<div className="flex-1">
						<h3 className="text-lg font-semibold text-gray-900 mb-2">
							{getTestTitle()}
						</h3>
						<div className="flex items-center gap-4 text-sm text-gray-600">
							<div className="flex items-center gap-1">
								<Clock className="w-4 h-4" />
								<span>last scored: {getLastScore()}</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="w-4 h-4" />
								<span>last attempted: {formatDate(test.createdAt)}</span>
							</div>
						</div>
					</div>
					<Bookmark className="w-5 h-5 text-purple-600 fill-purple-600" />
				</div>

				<Button
					onClick={handleAttempt}
					className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center gap-2"
				>
					Attempt
					<ChevronRight className="w-4 h-4" />
				</Button>
			</CardContent>
		</Card>
	);
};

const SavedMCQCard = ({ mcq }: { mcq: QuizMCQ }) => {
	return <li className="mb-2">{mcq.title}</li>;
};

const SavedPage = () => {
	const [activeTab, setActiveTab] = useState("tests");
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Fetch saved tests
	const {
		data: savedTestsData,
		isLoading: testsLoading,
		error: testsError,
	} = useGetBookmarks({
		category: "quiz",
		page: 1,
		limit: 20,
	});

	// Fetch saved MCQs
	const {
		data: savedMCQsData,
		isLoading: mcqsLoading,
		error: mcqsError,
	} = useGetBookmarks({
		category: "mcq",
		page: 1,
		limit: 20,
	});

	const savedTests = savedTestsData?.quizzes || [];
	const savedMCQs = savedMCQsData?.mcqs || [];

	if (testsError || mcqsError) {
		toast({
			title: "Error",
			description: "Failed to load saved items. Please try again.",
			variant: "destructive",
		});
	}

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}

			<div className="container mx-auto p-4 pb-20">
				{!isDesktop && <MobileHeader title="Saved" />}

				<div className="max-w-6xl mx-auto">
					<h1 className="text-2xl font-bold text-gray-900 mb-6">Saved</h1>

					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="grid w-full grid-cols-2 mb-6">
							<TabsTrigger value="mcqs">Saved MCQs</TabsTrigger>
							<TabsTrigger value="tests">Saved Tests</TabsTrigger>
						</TabsList>

						<TabsContent value="mcqs" className="space-y-4">
							{mcqsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-32 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedMCQs.length > 0 ? (
								<div>
									{savedMCQs.map((mcq: QuizMCQ) => (
										<SavedMCQCard key={mcq._id} mcq={mcq} />
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved MCQs
									</h3>
									<p className="text-gray-600">
										Start bookmarking MCQs to see them here.
									</p>
								</div>
							)}
						</TabsContent>

						<TabsContent value="tests" className="space-y-4">
							{testsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-40 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedTests.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{savedTests.map((test: SavedTest) => (
										<SavedTestCard key={test._id} test={test} />
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										No saved tests
									</h3>
									<p className="text-gray-600">
										Start bookmarking tests to see them here.
									</p>
								</div>
							)}
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/saved")({
	component: SavedPage,
});
